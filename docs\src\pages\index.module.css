/**
 * CSS files with the .module.css suffix will be treated as CSS modules
 * and scoped locally.
 */

.heroBanner {
  padding: 6rem 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.heroBanner::before {
  content: '';
  position: absolute;
  left: -25%;
  top: 50%;
  transform: translateY(-50%);
  width: 55%;
  height: 95%;
  background-image: url('/img/logo-neon.svg');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center right;
  opacity: 0.2;
  z-index: 0;
  filter: 
    blur(1px)
    drop-shadow(0 0 30px rgba(168, 85, 247, 0.8))
    drop-shadow(0 0 60px rgba(59, 130, 246, 0.6))
    drop-shadow(0 0 90px rgba(236, 72, 153, 0.4))
    drop-shadow(0 0 120px rgba(16, 185, 129, 0.3));
  animation: logoGlow 4s ease-in-out infinite alternate;
}

@keyframes logoGlow {
  0% {
    opacity: 0.15;
    filter: 
      blur(1px)
      drop-shadow(0 0 20px rgba(168, 85, 247, 0.6))
      drop-shadow(0 0 40px rgba(59, 130, 246, 0.4))
      drop-shadow(0 0 60px rgba(236, 72, 153, 0.3))
      drop-shadow(0 0 80px rgba(16, 185, 129, 0.2));
  }
  100% {
    opacity: 0.25;
    filter: 
      blur(0.5px)
      drop-shadow(0 0 40px rgba(168, 85, 247, 1.0))
      drop-shadow(0 0 80px rgba(59, 130, 246, 0.8))
      drop-shadow(0 0 120px rgba(236, 72, 153, 0.6))
      drop-shadow(0 0 160px rgba(16, 185, 129, 0.4));
  }
}

@media screen and (max-width: 966px) {
  .heroBanner {
    padding: 2rem;
  }
}

.buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 2rem;
  position: relative;
  z-index: 2;
}

.heroBanner .container {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100%;
}

.heroContent {
  max-width: 60%;
  text-align: left;
  margin: 0 auto;
}

/* Architecture Diagram Section */
.architectureSection {
  padding: 20px 0 10px 0;
  background: transparent;
}

.architectureSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    linear-gradient(90deg, transparent 0%, rgba(139, 92, 246, 0.03) 50%, transparent 100%),
    linear-gradient(0deg, transparent 0%, rgba(16, 185, 129, 0.02) 50%, transparent 100%);
  pointer-events: none;
  z-index: -1;
}

/* Node Styles */

/* Section Labels */
.sectionLabel {
  color: #ffffff;
  font-size: 18px;
  font-weight: 700;
  text-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
  background: rgba(0, 0, 0, 0.3);
  padding: 8px 16px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(139, 92, 246, 0.3);
}

.features {
  padding: 2rem 0 1rem 0;
}

.featureRowSpacing {
  margin-top: 2rem !important;
}

.features .row {
  margin-bottom: 0 !important;
}

.features .row:last-child {
  margin-bottom: 0 !important;
}

.features .col {
  margin-bottom: 0 !important;
  padding: 0 0.5rem;
}

.glassContainer {
  height: 100%;
  padding: 1.2rem;
  border-radius: 16px;
  transition: all 0.3s ease;
  position: relative;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(168, 85, 247, 0.3);
  box-shadow: 0 15px 35px -10px rgba(0, 0, 0, 0.8);
  margin-bottom: 0 !important;
  overflow: hidden; /* Ensures glows stay within border-radius */
}

.glassContainer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #a855f7, #3b82f6, #ec4899, #10b981);
  box-shadow: 
    0 0 20px 4px rgba(168, 85, 247, 0.4),
    inset 0 0 20px 2px rgba(168, 85, 247, 0.2);
  border-radius: 16px 16px 0 0;
}

.glassContainer::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(to bottom, rgba(168, 85, 247, 0.2), rgba(168, 85, 247, 0.05));
  border-radius: 16px 16px 0 0;
  pointer-events: none;
  z-index: 0;
}

.glassContainer > * {
  position: relative;
  z-index: 1;
}

.glassContainer:hover {
  transform: translateY(-8px);
  box-shadow: 
    0 30px 70px rgba(168, 85, 247, 0.3),
    0 0 40px rgba(168, 85, 247, 0.2);
  border-color: rgba(168, 85, 247, 0.5);
}

.glassContainer h3 {
  color: #ffffff;
  text-shadow: 0 0 15px rgba(168, 85, 247, 0.7);
  font-size: 1.15rem;
  font-weight: 700;
  margin: 0;
  line-height: 1.3;
  z-index: 10;
  position: relative;
  flex: 1;
}

.glassContainer p {
  color: #f8fafc;
  line-height: 1.6;
  font-size: 0.95rem;
  z-index: 10;
  position: relative;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
}

.featureHeader {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.featureIcon {
  color: #a855f7;
  filter: drop-shadow(0 0 10px rgba(168, 85, 247, 0.6));
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.featureDescription {
  color: #9ca3af !important;
  line-height: 1.6;
  font-size: 0.95rem;
  text-align: left;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
  margin: 0;
}

.glassContainer:hover .featureIcon {
  color: #c084fc;
  filter: drop-shadow(0 0 20px rgba(168, 85, 247, 0.8));
  transform: scale(1.1);
}

.quickStart {
  padding: 4rem 0;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(5px);
}

.nextSteps {
  padding: 4rem 0;
}

.callToAction {
  padding: 4rem 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
}

.diagramContainer {
  margin: 2rem 0;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 16px;
  border: 1px solid rgba(168, 85, 247, 0.2);
  backdrop-filter: blur(10px);
  overflow-x: auto;
}

.archonDiagram {
  padding: 2rem 0 1rem 0;
}

/* App Icon Styling */
.appIcon {
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s ease;
}

.appIcon:hover {
  transform: translateY(-8px);
}

.iconBox {
  width: 80px;
  height: 80px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.75rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 
    0 10px 30px rgba(0, 0, 0, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.iconBox:hover {
  transform: scale(1.1);
  box-shadow: 
    0 20px 50px rgba(168, 85, 247, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.cursorIcon, .windsurfIcon, .vscodeIcon {
  font-size: 2.5rem;
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.8));
}

.claudeIcon, .archonLogo {
  width: 50px;
  height: 50px;
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.8));
}

.ideIcon {
  width: 50px;
  height: 50px;
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.8));
}

.appName {
  color: #ffffff;
  font-weight: 600;
  font-size: 0.9rem;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* Neon Connector Lines */
.neonConnector {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 2rem 0;
  gap: 1rem;
}

.connectionLine {
  flex: 1;
  height: 2px;
  background: linear-gradient(90deg, transparent 0%, #a855f7 20%, #3b82f6 50%, #ec4899 80%, transparent 100%);
  box-shadow: 0 0 10px rgba(168, 85, 247, 0.8);
  animation: neonPulse 2s ease-in-out infinite alternate;
}

.connectionText {
  color: #a855f7;
  font-weight: 600;
  font-size: 0.9rem;
  text-shadow: 0 0 10px rgba(168, 85, 247, 0.8);
  background: rgba(0, 0, 0, 0.8);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  border: 1px solid rgba(168, 85, 247, 0.5);
  backdrop-filter: blur(10px);
}

@keyframes neonPulse {
  0% { opacity: 0.7; box-shadow: 0 0 5px rgba(168, 85, 247, 0.5); }
  100% { opacity: 1; box-shadow: 0 0 20px rgba(168, 85, 247, 1); }
}

/* Horizontal Connector for three-column layout */
.horizontalConnector {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin: 1rem 0;
}

.connectionArrow {
  font-size: 2rem;
  color: #a855f7;
  text-shadow: 0 0 15px rgba(168, 85, 247, 0.8);
  animation: arrowPulse 2s ease-in-out infinite alternate;
}

@keyframes arrowPulse {
  0% { 
    color: #a855f7;
    text-shadow: 0 0 10px rgba(168, 85, 247, 0.6);
    transform: scale(1);
  }
  100% { 
    color: #c084fc;
    text-shadow: 0 0 20px rgba(168, 85, 247, 1);
    transform: scale(1.1);
  }
}

/* IDE Node Styling */
.ideNode {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background: rgba(31, 41, 55, 0.8);
  border: 1px solid rgba(139, 92, 246, 0.5);
  border-radius: 12px;
  backdrop-filter: blur(8px);
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
  min-width: 100px;
  transition: all 0.3s ease;
}

.ideNode:hover {
  border-color: rgba(139, 92, 246, 0.8);
  box-shadow: 0 0 30px rgba(139, 92, 246, 0.5);
  transform: translateY(-2px);
}

.nodeIcon {
  width: 32px;
  height: 32px;
  margin-bottom: 6px;
  border-radius: 6px;
}

/* Make cursor logo white */
.ideNode img[alt="Cursor"] {
  filter: invert(1) brightness(1.2);
}

.nodeIconSvg {
  margin-bottom: 6px;
}

.nodeLabel {
  color: #ffffff;
  font-size: 12px;
  font-weight: 600;
  text-align: center;
  line-height: 1.2;
}

/* Archon Node Styling */
.archonNode {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 25px;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(168, 85, 247, 0.2));
  border: 2px solid rgba(139, 92, 246, 0.6);
  border-radius: 20px;
  backdrop-filter: blur(12px);
  box-shadow: 0 0 40px rgba(139, 92, 246, 0.4);
  min-width: 200px;
  min-height: 140px;
  position: relative;
  overflow: hidden;
}

.archonNode::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.archonIcon {
  width: 60px;
  height: 60px;
  margin-bottom: 12px;
}

.archonText h3 {
  color: #ffffff;
  font-size: 22px;
  font-weight: 700;
  margin: 0 0 6px 0;
  text-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
}

.archonText p {
  color: #9ca3af;
  font-size: 0.9rem;
  margin: 0;
  text-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
}

.pythonIcon {
  position: absolute;
  top: 8px;
  left: 8px;
  width: 24px;
  height: 24px;
  z-index: 10;
  filter: drop-shadow(0 0 8px rgba(255, 212, 59, 0.8));
}

/* Knowledge Node Styling */
.knowledgeNode {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.5);
  border-radius: 8px;
  backdrop-filter: blur(8px);
  box-shadow: 0 0 15px rgba(16, 185, 129, 0.2);
  min-width: 120px;
  transition: all 0.3s ease;
}

.knowledgeNode:hover {
  border-color: rgba(16, 185, 129, 0.8);
  box-shadow: 0 0 25px rgba(16, 185, 129, 0.4);
  transform: translateY(-2px);
}

.knowledgeNode .nodeIconSvg {
  color: #10b981;
}

.intelligenceNode {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.5);
  border-radius: 8px;
  backdrop-filter: blur(8px);
  box-shadow: 0 0 15px rgba(245, 158, 11, 0.2);
  min-width: 120px;
  transition: all 0.3s ease;
}

.intelligenceNode:hover {
  border-color: rgba(245, 158, 11, 0.8);
  box-shadow: 0 0 25px rgba(245, 158, 11, 0.4);
  transform: translateY(-2px);
}

.intelligenceNode .nodeIconSvg {
  color: #f59e0b;
}

.knowledgeHeader {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  gap: 0.75rem;
}

.knowledgeIcon {
  font-size: 2rem;
  filter: drop-shadow(0 0 10px rgba(59, 130, 246, 0.6));
}

.knowledgeNode h4 {
  margin: 0;
  color: #ffffff;
  font-size: 1.2rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.knowledgeFeatures {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

.featureItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 0.85rem;
  color: #d1d5db;
  transition: all 0.2s ease;
}

.featureItem:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
  color: #ffffff;
}

.featureIcon {
  font-size: 1rem;
  filter: drop-shadow(0 0 5px rgba(59, 130, 246, 0.4));
}

/* React Flow Edge Styling - SOLID GLOWING LINES */
:global(.react-flow__edge-path) {
  stroke-width: 3 !important;
  filter: drop-shadow(0 0 10px currentColor) !important;
  stroke-dasharray: none !important;
  animation: solidGlow 2s ease-in-out infinite alternate !important;
}

:global(.react-flow__edge) {
  pointer-events: none !important;
}

@keyframes solidGlow {
  0% {
    filter: drop-shadow(0 0 8px currentColor);
  }
  100% {
    filter: drop-shadow(0 0 20px currentColor);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .flowContainer {
    height: 500px;
  }
  
  .ideNode, .knowledgeNode {
    min-width: 100px;
    max-width: 200px;
  }
  
  .archonNode {
    min-width: 150px;
    padding: 1.5rem;
  }
  
  .knowledgeFeatures {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .heroBanner {
    padding: 3rem 0;
  }

  .heroBanner::before {
    left: -20%;
    width: 60%;
    height: 70%;
    opacity: 0.08;
  }

  .glassContainer {
    padding: 1rem;
    margin-bottom: 1.25rem;
  }

  .glassContainer h3 {
    font-size: 1rem;
  }

  .glassContainer p {
    font-size: 0.85rem;
  }

  .featureIcon {
    width: 36px;
    height: 36px;
  }

  .heroContent {
    max-width: 90%;
    text-align: center;
    margin: 0 auto;
  }

  .architectureSection, .quickStart, .nextSteps, .callToAction {
    padding: 2.5rem 0;
  }

  .diagramContainer {
    padding: 1rem;
    margin: 1rem 0;
  }

  .iconBox {
    width: 50px;
    height: 50px;
    border-radius: 12px;
  }

  .cursorIcon, .windsurfIcon, .vscodeIcon {
    font-size: 1.5rem;
  }

  .claudeIcon, .archonLogo {
    width: 25px;
    height: 25px;
  }

  .systemBox {
    padding: 1rem;
  }

  .systemBox h4 {
    font-size: 1.2rem;
  }

  .archonCore {
    padding: 1rem;
    max-width: 200px;
  }

  .archonCore h3 {
    font-size: 1.4rem;
  }

  .archonIconBox {
    width: 60px;
    height: 60px;
  }

  .powerFeature {
    padding: 1.25rem;
    margin-bottom: 1.25rem;
  }

  .powerFeature h3 {
    font-size: 1.1rem;
  }

  .powerFeature p {
    font-size: 0.9rem;
  }
}

/* Container Node Styling */
.containerNode {
  display: flex;
  flex-direction: column;
  padding: 16px;
  background: rgba(31, 41, 55, 0.8);
  border: 1px solid rgba(139, 92, 246, 0.5);
  border-radius: 12px;
  backdrop-filter: blur(8px);
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
  min-width: 200px;
  transition: all 0.3s ease;
}

.containerNode:hover {
  border-color: rgba(139, 92, 246, 0.8);
  box-shadow: 0 0 30px rgba(139, 92, 246, 0.5);
  transform: translateY(-2px);
}

/* Knowledge Sources - Green */
.containerNode[data-type="knowledge"] {
  border-color: rgba(16, 185, 129, 0.6);
  box-shadow: 0 0 25px rgba(16, 185, 129, 0.3);
}

.containerNode[data-type="knowledge"]:hover {
  border-color: rgba(16, 185, 129, 0.9);
  box-shadow: 0 0 40px rgba(16, 185, 129, 0.5);
}

.containerNode[data-type="knowledge"] .containerTitle {
  text-shadow: 0 0 10px rgba(16, 185, 129, 0.6);
}

/* Project Intelligence - Orange */
.containerNode[data-type="intelligence"] {
  border-color: rgba(245, 158, 11, 0.6);
  box-shadow: 0 0 25px rgba(245, 158, 11, 0.3);
}

.containerNode[data-type="intelligence"]:hover {
  border-color: rgba(245, 158, 11, 0.9);
  box-shadow: 0 0 40px rgba(245, 158, 11, 0.5);
}

.containerNode[data-type="intelligence"] .containerTitle {
  text-shadow: 0 0 10px rgba(245, 158, 11, 0.6);
}

.containerTitle {
  color: #ffffff;
  font-size: 16px;
  font-weight: 700;
  margin: 0 0 12px 0;
  text-align: center;
  text-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
}

.containerGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.containerItem {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  transition: all 0.2s ease;
}

.containerItem:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(139, 92, 246, 0.3);
}

.itemIcon {
  color: #8b5cf6;
  flex-shrink: 0;
}

.itemLabel {
  color: #ffffff;
  font-size: 11px;
  font-weight: 500;
  line-height: 1.2;
}

/* UI Control Node Styling */
.uiControlNode {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background: rgba(31, 41, 55, 0.9);
  border: 1px solid rgba(59, 130, 246, 0.6);
  border-radius: 12px;
  backdrop-filter: blur(8px);
  box-shadow: 0 0 25px rgba(59, 130, 246, 0.3);
  min-width: 180px;
  transition: all 0.3s ease;
}

.uiControlNode:hover {
  border-color: rgba(59, 130, 246, 0.8);
  box-shadow: 0 0 35px rgba(59, 130, 246, 0.5);
  transform: translateY(-2px);
}

.uiControlTitle {
  color: #ffffff;
  font-size: 1.3rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 0 15px rgba(59, 130, 246, 0.7);
  text-align: center;
}

.reactIcon {
  position: absolute;
  top: 8px;
  left: 8px;
  width: 24px;
  height: 24px;
  z-index: 10;
  filter: drop-shadow(0 0 8px rgba(97, 218, 251, 0.8));
}

.uiControlSubtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 13px;
  margin: 0;
  text-align: center;
  line-height: 1.3;
}

.mcpNode {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(139, 92, 246, 0.2);
  border: 2px solid #8b5cf6;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  box-shadow: 
    0 0 20px rgba(139, 92, 246, 0.6),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.mcpNode:hover {
  transform: scale(1.1);
  box-shadow: 
    0 0 30px rgba(139, 92, 246, 0.8),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.mcpIcon {
  width: 40px;
  height: 40px;
  filter: brightness(0) invert(1) drop-shadow(0 0 10px rgba(255, 255, 255, 0.8));
}

.fastapiNode {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 150, 136, 0.2);
  border: 2px solid #009688;
  border-radius: 12px;
  padding: 8px 12px;
  min-width: 120px;
  height: 40px;
  box-shadow: 
    0 0 20px rgba(0, 150, 136, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.fastapiNode:hover {
  transform: scale(1.05);
  box-shadow: 
    0 0 30px rgba(0, 150, 136, 0.6),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.fastapiIcon {
  height: 24px;
  width: auto;
  filter: brightness(0) invert(1) drop-shadow(0 0 8px rgba(255, 255, 255, 0.6));
}



