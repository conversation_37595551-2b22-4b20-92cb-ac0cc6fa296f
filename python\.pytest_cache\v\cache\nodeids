["tests/test_async_credential_service.py::TestAsyncCredentialService::test_get_active_provider_basic", "tests/test_async_credential_service.py::TestAsyncCredentialService::test_get_active_provider_llm", "tests/test_async_llm_provider_service.py::TestAsyncLLMProviderService::test_get_embedding_model_openrouter_success", "tests/test_async_llm_provider_service.py::TestAsyncLLMProviderService::test_get_llm_client_missing_openrouter_key", "tests/test_async_llm_provider_service.py::TestAsyncLLMProviderService::test_get_llm_client_openrouter_success"]